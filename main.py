from DrissionPage import ChromiumPage,ChromiumOptions
import time

def find_dm_ele(time_out,page):
    time.sleep(time_out)

    # 查找私信按钮
    dm_ele = page.ele('tag:span@@class=semi-button-content@@text()=私信')

    if dm_ele:
        print(f"找到私信按钮: {dm_ele.text}")
        # 使用JavaScript点击，避免位置问题
        dm_ele.click(by_js=True)
        time.sleep(1)
        dm_ele.click(by_js=True)
        print("私信按钮点击成功！")
    else:
        print("未找到私信按钮")


def main():
    #设置打开主页延迟
    time_out = 5
    #设置sec_uid
    sec_uid = 'MS4wLjABAAAAA5KVTD27RglZ7ZrTpb0cRz8qu2HVX-lyjAKKcSwBXHF-yCgklGtrPkGMwNiJ254x'
    #设置浏览器选项
    co = ChromiumOptions()
    #设置无图模式
    # co.no_imgs(True)
    #设置无头模式
    co.headless(False)
    page = ChromiumPage(co)

    
    zhuye_url = f'https://www.douyin.com/user/{sec_uid}?from_tab_name=main'
    page.get(zhuye_url)
    print(page.title)

    # 等待页面加载
    # page.wait.load_start()
    find_dm_ele(time_out,page)

    time.sleep(time_out)

    dm_input = page.ele('text:发送消息')
    if dm_input:
        print(f"找到发送消息输入框: {dm_input.text}")
        # 使用JavaScript点击，避免位置问题
        dm_input.click(by_js=True)
        time.sleep(1)
        dm_input.click(by_js=True)
        print("发送消息输入框点击成功！")
    else:
        print("未找到发送消息输入框")

    #输入消息
    page.send_keys('text:发送消息', 'xiao xi')
    # page.close()

if __name__ == '__main__':
    main()