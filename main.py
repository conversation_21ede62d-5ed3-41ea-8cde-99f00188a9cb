from DrissionPage import ChromiumPage,ChromiumOptions
import time

def find_dm_ele(time_out,page):
    time.sleep(time_out)

    # 查找私信按钮
    dm_ele = page.ele('tag:span@@class=semi-button-content@@text()=私信')

    if dm_ele:
        print(f"找到私信按钮: {dm_ele.text}")
        # 使用JavaScript点击，避免位置问题
        dm_ele.click(by_js=True)
        time.sleep(1)
        dm_ele.click(by_js=True)
        print("私信按钮点击成功！")
    else:
        print("未找到私信按钮")


def main():
    #设置打开主页延迟
    time_out = 5
    #设置sec_uid
    sec_uid = 'MS4wLjABAAAAA5KVTD27RglZ7ZrTpb0cRz8qu2HVX-lyjAKKcSwBXHF-yCgklGtrPkGMwNiJ254x'
    #设置浏览器选项
    co = ChromiumOptions()
    #设置无图模式
    # co.no_imgs(True)
    #设置无头模式
    co.headless(False)
    page = ChromiumPage(co)

    
    zhuye_url = f'https://www.douyin.com/user/{sec_uid}?from_tab_name=main'
    page.get(zhuye_url)
    print(page.title)

    # 等待页面加载
    # page.wait.load_start()
    find_dm_ele(time_out,page)

    time.sleep(time_out)

    dm_input = page.ele('text:发送消息')
    if dm_input:
        print(f"找到发送消息输入框: {dm_input.text}")
        # 使用JavaScript点击，避免位置问题
        dm_input.click(by_js=True)
        time.sleep(1)
        dm_input.click(by_js=True)
        print("发送消息输入框点击成功！")
    else:
        print("未找到发送消息输入框")

    #输入消息
    message = "你好！这是一条测试消息。"

    # 根据提供的HTML结构查找输入框
    input_selectors = [
        # 最外层容器
        '[data-e2e="msg-input"]',
        # 可编辑的内容区域
        '.public-DraftEditor-content[contenteditable="true"]',
        # 组合选择器
        'tag:div@data-e2e=msg-input .public-DraftEditor-content',
        # 备用选择器
        '[contenteditable="true"][role="textbox"]'
    ]

    input_ele = None
    for selector in input_selectors:
        input_ele = page.ele(selector)
        if input_ele:
            print(f"找到输入框: {selector}")
            break

    if input_ele:
        try:
            # 点击输入框获得焦点
            input_ele.click()
            time.sleep(0.5)

            # 清空内容（如果有的话）
            input_ele.clear()

            # 输入消息
            input_ele.input(message)
            print(f"消息输入成功: {message}")

            time.sleep(1)

            # 查找发送按钮（通常在输入框附近）
            send_selectors = [
                'text:发送',
                'tag:button@@text():发送',
                # 可能是SVG图标按钮
                'tag:svg',
                # 在消息输入容器内查找按钮
                '[data-e2e="msg-input"] button',
                '[data-e2e="msg-input"] svg'
            ]

            send_btn = None
            for selector in send_selectors:
                send_btn = page.ele(selector)
                if send_btn:
                    print(f"找到发送按钮: {selector}")
                    break

            if send_btn:
                send_btn.click()
                print("消息发送成功！")
            else:
                print("未找到发送按钮，尝试按回车键发送")
                # 按回车键发送
                from DrissionPage.common import Keys
                input_ele.input(Keys.ENTER)
                print("已按回车键发送")

        except Exception as e:
            print(f"输入消息失败: {e}")
    else:
        print("未找到消息输入框")
        # 打印页面上所有可能的输入元素用于调试
        print("页面上的可编辑元素:")
        editable_elements = page.eles('[contenteditable="true"]')
        for i, ele in enumerate(editable_elements):
            print(f"  {i+1}. {ele.tag} - {ele.attrs}")

    
    # page.close()

if __name__ == '__main__':
    main()