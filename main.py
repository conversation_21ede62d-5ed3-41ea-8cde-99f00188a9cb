from DrissionPage import ChromiumPage,ChromiumOptions


def main():
    co = ChromiumOptions()
    # co.no_imgs(True)
    co.headless(False)
    
    page = ChromiumPage(co)

    sec_uid = 'MS4wLjABAAAAA5KVTD27RglZ7ZrTpb0cRz8qu2HVX-lyjAKKcSwBXHF-yCgklGtrPkGMwNiJ254x'
    zhuye_url = f'https://www.douyin.com/user/{sec_uid}?from_tab_name=main'
    page.get(zhuye_url)
    print(page.title)

    # 等待页面完全加载
    page.wait.load_start()

    dm_ele = page.ele('tag:span@@class=semi-button-content@@text()=私信')

    if dm_ele:
        print(f"找到私信按钮: {dm_ele.text}")

        # 诊断元素状态
        print(f"元素是否可见: {dm_ele.states.is_displayed}")
        print(f"元素是否可用: {dm_ele.states.is_enabled}")

        try:
            # 检查元素位置信息
            location = dm_ele.rect.location
            size = dm_ele.rect.size
            print(f"元素位置: {location}")
            print(f"元素大小: {size}")
        except:
            print("无法获取元素位置和大小信息")

        try:
            # 方法1: 滚动到元素位置
            print("尝试滚动到元素位置...")
            dm_ele.scroll.to_see()

            # 等待元素变为可点击状态
            print("等待元素变为可点击...")
            page.wait.ele_displayed(dm_ele, timeout=5)

            # 尝试点击
            print("尝试点击私信按钮...")
            dm_ele.click()
            print("点击成功！")

        except Exception as e:
            print(f"常规点击失败: {e}")

            # 方法2: 使用JavaScript点击
            try:
                print("尝试使用JavaScript点击...")
                dm_ele.click(by_js=True)
                print("JavaScript点击成功！")
            except Exception as e2:
                print(f"JavaScript点击也失败: {e2}")

                # 方法3: 查找其他相关元素
                print("尝试查找其他相关元素...")
                dm_elements = page.eles('私信')
                print(f"找到 {len(dm_elements)} 个包含'私信'的元素")

                for i, ele in enumerate(dm_elements):
                    print(f"元素 {i+1}: {ele.tag} - 可见: {ele.states.is_displayed}")
                    if ele.states.is_displayed:
                        try:
                            ele.click()
                            print(f"成功点击元素 {i+1}！")
                            break
                        except:
                            continue
    else:
        print("未找到私信按钮")
    # page.close()

if __name__ == '__main__':
    main()